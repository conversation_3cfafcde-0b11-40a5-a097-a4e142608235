<?php

require_once __DIR__ . '/vendor/autoload.php';

use common\enums\SchoolPaperStatus;
use common\enums\SchoolPaperAuditStatus;

// 测试SchoolPaperStatus枚举
echo "=== SchoolPaperStatus 枚举测试 ===\n";

// 测试枚举值
echo "Pending value: " . SchoolPaperStatus::Pending->value . "\n";
echo "Approved value: " . SchoolPaperStatus::Approved->value . "\n";
echo "Rejected value: " . SchoolPaperStatus::Rejected->value . "\n";

// 测试标签
echo "Pending label: " . SchoolPaperStatus::Pending->label() . "\n";
echo "Approved label: " . SchoolPaperStatus::Approved->label() . "\n";
echo "Rejected label: " . SchoolPaperStatus::Rejected->label() . "\n";

// 测试方法
echo "Pending isPending: " . (SchoolPaperStatus::Pending->isPending() ? 'true' : 'false') . "\n";
echo "Approved isApproved: " . (SchoolPaperStatus::Approved->isApproved() ? 'true' : 'false') . "\n";
echo "Rejected isRejected: " . (SchoolPaperStatus::Rejected->isRejected() ? 'true' : 'false') . "\n";

echo "\n=== SchoolPaperAuditStatus 枚举测试 ===\n";

// 测试枚举值
echo "Submitted value: " . SchoolPaperAuditStatus::Submitted->value . "\n";
echo "Approved value: " . SchoolPaperAuditStatus::Approved->value . "\n";
echo "Rejected value: " . SchoolPaperAuditStatus::Rejected->value . "\n";

// 测试标签
echo "Submitted label: " . SchoolPaperAuditStatus::Submitted->label() . "\n";
echo "Approved label: " . SchoolPaperAuditStatus::Approved->label() . "\n";
echo "Rejected label: " . SchoolPaperAuditStatus::Rejected->label() . "\n";

// 测试方法
echo "Submitted isSubmitted: " . (SchoolPaperAuditStatus::Submitted->isSubmitted() ? 'true' : 'false') . "\n";
echo "Approved isApproved: " . (SchoolPaperAuditStatus::Approved->isApproved() ? 'true' : 'false') . "\n";
echo "Rejected isRejected: " . (SchoolPaperAuditStatus::Rejected->isRejected() ? 'true' : 'false') . "\n";

echo "\n=== 枚举值对应关系测试 ===\n";
echo "SchoolPaperStatus::Pending->value === SchoolPaperAuditStatus::Submitted->value: " . 
     (SchoolPaperStatus::Pending->value === SchoolPaperAuditStatus::Submitted->value ? 'true' : 'false') . "\n";
echo "SchoolPaperStatus::Approved->value === SchoolPaperAuditStatus::Approved->value: " . 
     (SchoolPaperStatus::Approved->value === SchoolPaperAuditStatus::Approved->value ? 'true' : 'false') . "\n";
echo "SchoolPaperStatus::Rejected->value === SchoolPaperAuditStatus::Rejected->value: " . 
     (SchoolPaperStatus::Rejected->value === SchoolPaperAuditStatus::Rejected->value ? 'true' : 'false') . "\n";

echo "\n测试完成！\n";
