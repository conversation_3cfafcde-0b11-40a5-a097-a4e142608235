<?php

namespace common\models\paper;

use common\enums\SchoolPaperAuditStatus;
use common\enums\SchoolPaperStatus;
use common\models\base\User;
use common\models\school\School;
use common\validators\EnumValidator;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * 校本试卷模型
 *
 * @property int $id
 * @property int $paper_id 试卷ID
 * @property int $school_id 学校ID
 * @property SchoolPaperStatus $status 状态：待审核、已通过、已退回
 * @property int|null $audit_user_id 审核人ID
 * @property int|null $audit_time 审核时间
 * @property string|null $audit_comment 审核意见
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int|null $created_by 创建人ID
 * @property int|null $updated_by 更新人ID
 *
 * @property School $school 学校
 * @property User $creator 创建人
 * @property User $updater 更新人
 * @property User $auditor 审核人
 * @property SchoolPaperTagRelation[] $tagRelations 标签关联
 * @property SchoolPaperAuditLog[] $auditLogs 审核日志
 */
class SchoolPaper extends ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%school_paper}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        return [
            TimestampBehavior::class,
            BlameableBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['paper_id', 'school_id'], 'required'],
            [['paper_id', 'school_id', 'audit_user_id', 'audit_time', 'created_at', 'updated_at', 'created_by', 'updated_by'], 'integer'],
            [['audit_comment'], 'string'],
            [['status'], EnumValidator::class, 'enumClass' => SchoolPaperStatus::class],
            [['status'], 'default', 'value' => SchoolPaperStatus::Pending],

            // 唯一性验证
            [['paper_id'], 'unique', 'targetAttribute' => ['paper_id', 'school_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'paper_id' => '试卷ID',
            'school_id' => '学校ID',
            'status' => '状态',
            'audit_user_id' => '审核人ID',
            'audit_time' => '审核时间',
            'audit_comment' => '审核意见',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'created_by' => '创建人',
            'updated_by' => '更新人',
        ];
    }

    /**
     * 获取学校关联
     */
    public function getSchool()
    {
        return $this->hasOne(School::class, ['id' => 'school_id']);
    }

    /**
     * 获取创建人关联
     */
    public function getCreator()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * 获取更新人关联
     */
    public function getUpdater()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    /**
     * 获取审核人关联
     */
    public function getAuditor()
    {
        return $this->hasOne(User::class, ['id' => 'audit_user_id']);
    }

    /**
     * 获取标签关联关系
     */
    public function getTagRelations()
    {
        return $this->hasMany(SchoolPaperTagRelation::class, ['paper_id' => 'paper_id']);
    }

    /**
     * 获取审核日志
     */
    public function getAuditLogs()
    {
        return $this->hasMany(SchoolPaperAuditLog::class, ['paper_id' => 'paper_id', 'school_id' => 'school_id']);
    }

    /**
     * 获取状态文本
     */
    public function getStatusText(): string
    {
        return $this->status instanceof SchoolPaperStatus ? $this->status->label() : '未知状态';
    }

    /**
     * 判断是否为待审核状态
     */
    public function isPending(): bool
    {
        return $this->status === SchoolPaperStatus::Pending;
    }

    /**
     * 判断是否为已通过状态
     */
    public function isApproved(): bool
    {
        return $this->status === SchoolPaperStatus::Approved;
    }

    /**
     * 判断是否为已退回状态
     */
    public function isRejected(): bool
    {
        return $this->status === SchoolPaperStatus::Rejected;
    }

    /**
     * 提交审核
     *
     * @param int $paperId 试卷ID
     * @param int $schoolId 学校ID
     * @param int|null $createdBy 创建人ID
     * @return bool|static
     */
    public static function submitForAudit(int $paperId, int $schoolId, ?int $createdBy = null)
    {
        // 检查是否已存在
        $existing = static::find()
            ->where(['paper_id' => $paperId, 'school_id' => $schoolId])
            ->one();

        if ($existing) {
            // 如果已存在，更新状态为待审核
            $existing->status = SchoolPaperStatus::Pending;
            $existing->audit_user_id = null;
            $existing->audit_time = null;
            $existing->audit_comment = null;
            
            if ($existing->save()) {
                // 记录审核日志
                SchoolPaperAuditLog::log($paperId, $schoolId, SchoolPaperAuditStatus::Submitted, null, $createdBy);
                return $existing;
            }
            return false;
        }

        // 创建新记录
        $schoolPaper = new static();
        $schoolPaper->paper_id = $paperId;
        $schoolPaper->school_id = $schoolId;
        $schoolPaper->status = SchoolPaperStatus::Pending;
        
        if ($createdBy !== null) {
            $schoolPaper->created_by = $createdBy;
        }

        if ($schoolPaper->save()) {
            // 记录审核日志
            SchoolPaperAuditLog::log($paperId, $schoolId, SchoolPaperAuditStatus::Submitted, null, $createdBy);
            return $schoolPaper;
        }

        return false;
    }

    /**
     * 审核通过
     *
     * @param string|null $comment 审核意见
     * @param int|null $auditUserId 审核人ID
     * @return bool
     */
    public function approve(?string $comment = null, ?int $auditUserId = null): bool
    {
        $this->status = SchoolPaperStatus::Approved;
        $this->audit_comment = $comment;
        $this->audit_time = time();
        
        if ($auditUserId !== null) {
            $this->audit_user_id = $auditUserId;
        }

        if ($this->save()) {
            // 记录审核日志
            SchoolPaperAuditLog::log($this->paper_id, $this->school_id, SchoolPaperAuditStatus::Approved, $comment, $auditUserId);
            return true;
        }

        return false;
    }

    /**
     * 审核退回
     *
     * @param string|null $comment 审核意见
     * @param int|null $auditUserId 审核人ID
     * @return bool
     */
    public function reject(?string $comment = null, ?int $auditUserId = null): bool
    {
        $this->status = SchoolPaperStatus::Rejected;
        $this->audit_comment = $comment;
        $this->audit_time = time();
        
        if ($auditUserId !== null) {
            $this->audit_user_id = $auditUserId;
        }

        if ($this->save()) {
            // 记录审核日志
            SchoolPaperAuditLog::log($this->paper_id, $this->school_id, SchoolPaperAuditStatus::Rejected, $comment, $auditUserId);
            return true;
        }

        return false;
    }

    /**
     * 获取学校的校本试卷统计
     *
     * @param int $schoolId 学校ID
     * @return array
     */
    public static function getSchoolStatistics(int $schoolId): array
    {
        return [
            'pending' => static::find()->where(['school_id' => $schoolId, 'status' => SchoolPaperStatus::Pending])->count(),
            'approved' => static::find()->where(['school_id' => $schoolId, 'status' => SchoolPaperStatus::Approved])->count(),
            'rejected' => static::find()->where(['school_id' => $schoolId, 'status' => SchoolPaperStatus::Rejected])->count(),
        ];
    }
}
