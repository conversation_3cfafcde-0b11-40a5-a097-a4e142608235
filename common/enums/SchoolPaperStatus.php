<?php

namespace common\enums;

enum SchoolPaperStatus: int implements EnumLabelInterface
{
    use EnumLabelTrait;

    case Pending = 0;   // 待审核
    case Approved = 1;  // 已通过
    case Rejected = 2;  // 已退回

    /**
     * @inheritDoc
     */
    public static function labels(): array
    {
        return [
            self::Pending->value => '待审核',
            self::Approved->value => '已通过',
            self::Rejected->value => '已退回',
        ];
    }

    /**
     * 判断是否为待审核状态
     */
    public function isPending(): bool
    {
        return $this === self::Pending;
    }

    /**
     * 判断是否为已通过状态
     */
    public function isApproved(): bool
    {
        return $this === self::Approved;
    }

    /**
     * 判断是否为已退回状态
     */
    public function isRejected(): bool
    {
        return $this === self::Rejected;
    }
}
