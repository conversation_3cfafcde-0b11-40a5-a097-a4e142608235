<?php

namespace common\enums;

enum SchoolPaperAuditStatus: int implements EnumLabelInterface
{
    use EnumLabelTrait;

    case Submitted = 0; // 提交审核
    case Approved = 1;  // 审核通过
    case Rejected = 2;  // 审核退回

    /**
     * @inheritDoc
     */
    public static function labels(): array
    {
        return [
            self::Submitted->value => '提交审核',
            self::Approved->value => '审核通过',
            self::Rejected->value => '审核退回',
        ];
    }

    /**
     * 判断是否为提交审核状态
     */
    public function isSubmitted(): bool
    {
        return $this === self::Submitted;
    }

    /**
     * 判断是否为审核通过状态
     */
    public function isApproved(): bool
    {
        return $this === self::Approved;
    }

    /**
     * 判断是否为审核退回状态
     */
    public function isRejected(): bool
    {
        return $this === self::Rejected;
    }
}
