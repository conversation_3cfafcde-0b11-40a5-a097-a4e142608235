# 校本作业库模块开发步骤

> **版本**: v1.0.0
> **最后更新**: 2024-07-22
> **状态**: 开发中 🚧

## 📝 更新日志

### v1.0.0 (2024-07-22)
- ✅ 完成数据库表设计和迁移文件
- ✅ 完成所有核心模型类开发
- ✅ 完成权限系统设计和实现
- ✅ 完成开发文档编写
- 📋 待开发：控制器层和前端界面

## 📋 目录

1. [项目概述](#项目概述)
2. [技术架构](#技术架构)
3. [开发计划](#开发计划)
4. [数据库设计](#数据库设计)
5. [核心业务逻辑实现](#核心业务逻辑实现)
6. [API接口设计](#api接口设计)
7. [权限控制实现](#权限控制实现)
8. [开发进度跟踪](#开发进度跟踪)
9. [测试计划](#测试计划)
10. [部署说明](#部署说明)

## 🎯 项目概述

### 业务背景
校本作业库模块是一个多校SaaS系统的重要组成部分，旨在为学校提供独立的试卷资源管理功能。系统支持：
- 🏫 **多校隔离**：每个学校拥有独立的校本题库
- 👥 **权限分级**：支持课程组长、教师等不同角色权限
- 📝 **审核流程**：完整的试卷提交、审核、发布流程
- 🏷️ **标签管理**：灵活的多级标签分类系统
- 📊 **数据统计**：丰富的统计分析功能

### 核心功能模块
1. **试卷管理**：试卷提交、审核、发布
2. **标签系统**：多级标签分类管理
3. **权限控制**：基于角色的权限管理
4. **审核流程**：完整的审核工作流
5. **数据统计**：多维度数据分析
6. **检索功能**：强大的试卷检索能力

## 🏗️ 技术架构

### 数据库层
- **主表**：`school_paper` - 校本试卷主表
- **标签系统**：`school_paper_tag`, `school_paper_tag_relation` - 标签管理
- **审核系统**：`school_paper_audit_log` - 审核流程记录
- **权限系统**：`course_leader_scope` - 课程组长权限范围

### 应用层
- **模型层**：5个核心ActiveRecord模型
- **服务层**：业务逻辑封装，权限管理服务
- **控制器层**：Web界面控制器 + API控制器
- **权限层**：基于Yii2 RBAC的权限控制

### 前端层
- **管理后台**：标签管理、审核管理、数据看板
- **教师端**：试卷检索、提交、状态跟踪
- **API接口**：RESTful API支持移动端和第三方集成

校本作业库模块旨在现有教学管理系统基础上叠加校本资源管理能力，构建"学校自主题库"闭环，提升教学资源利用效率和质量管控水平。该模块将实现优质教学资源的沉淀、审核、标签化管理和复用。

## 开发阶段划分

根据产品需求分析文档，项目开发分为三个阶段，总计11周时间：

1. 第一阶段（4周）：数据库设计和创建、基础标签管理功能、简单审核流程
2. 第二阶段（4周）：管理者看板开发、教师端检索功能、权限系统集成
3. 第三阶段（3周）：高级功能完善、系统测试和优化、用户培训和上线

## 详细开发步骤

### 第一阶段（4周）

#### 第1周：数据库设计与创建

1. **数据库表设计**
   - 修改 `school` 表，添加 `enable_school_tiku` 字段
   - 创建 `school_paper` 表（校本资源表）
     - 包含字段：id, paper_id, school_id, status, audit_user_id, audit_time, audit_comment, created_at, updated_at
   - 创建 `school_paper_tag` 表（校本标签表）
     - 包含字段：id, name, school_id, course_code, type(系统/自定义), parent_id, created_at, updated_at, created_by, updated_by
   - 创建 `school_paper_tag_relation` 表（校本试卷标签关联表）
     - 包含字段：id, paper_id, tag_id, is_main, created_at
   - 创建 `school_paper_audit_log` 表（校本审核流水表）
     - 包含字段：id, paper_id, school_id, status, comment, created_by, created_at
   - 修改 `knowledge_tree` 表，添加 `school_id` 字段
   - 修改 `textbook_version` 表，添加 `school_id` 字段

2. **数据库迁移文件创建**
   - 创建迁移文件 `m230501_000001_create_school_paper_tables.php`
   - 创建迁移文件 `m230501_000002_add_school_id_to_knowledge_tree.php`
   - 创建迁移文件 `m230501_000003_add_school_id_to_textbook_version.php`

3. **模型类创建**
   - 创建 `SchoolPaper` 模型类
   - 创建 `SchoolPaperTag` 模型类
   - 创建 `SchoolPaperTagRelation` 模型类
   - 创建 `SchoolPaperAuditLog` 模型类
   - 更新 `KnowledgeTree` 模型类
   - 更新 `TextbookVersion` 模型类

#### 第2周：基础标签管理功能

1. **后台管理界面开发**
   - 创建校本标签管理控制器 `SchoolPaperTagController`
   - 开发标签列表页面
   - 开发标签创建/编辑页面
   - 开发标签删除功能
   - 实现标签多级分类管理

2. **标签API接口开发**
   - 创建校本标签API控制器 `SchoolPaperTagApiController`
   - 开发获取标签列表接口
   - 开发创建/编辑标签接口
   - 开发删除标签接口
   - 开发标签排序接口

3. **预置基础标签库**
   - 创建系统级标签数据迁移文件
   - 实现系统级标签不可删除的逻辑

#### 第3-4周：简单审核流程

1. **审核流程后台管理**
   - 创建审核管理控制器 `SchoolPaperAuditController`
   - 开发待审核列表页面
   - 开发审核详情页面
   - 实现审核通过/退回功能
   - 实现批量审核功能
   - 实现审核批注功能

2. **教师端上传功能**
   - 修改现有试卷编辑页面，添加"提交到校本题库"按钮
   - 实现试卷提交到校本题库的功能
   - 实现试卷与校本库的关联关系

3. **审核API接口开发**
   - 创建审核API控制器 `SchoolPaperAuditApiController`
   - 开发获取待审核列表接口
   - 开发审核通过/退回接口
   - 开发获取审核历史接口

### 第二阶段（4周）

#### 第5-6周：管理者看板开发

1. **数据统计功能**
   - 创建校本题库数据统计服务类 `SchoolPaperStatisticsService`
   - 实现按学科、年级维度统计题库总量
   - 实现资源来源分布统计
   - 实现高频使用标签TOP10排行
   - 实现作业量监控

2. **管理者看板界面**
   - 创建校本题库看板控制器 `SchoolPaperDashboardController`
   - 开发看板页面，包含各类统计图表
   - 实现PDF格式简报导出功能
   - 实现题库健康度指标展示

3. **权限控制**
   - 实现校级管理员全校数据查看权限
   - 实现学科组长仅限本学科数据查看权限

#### 第7-8周：教师端检索功能与权限系统集成

1. **教师端检索功能**
   - 创建校本题库检索控制器 `SchoolPaperSearchController`
   - 开发校本题库入口标签页
   - 实现按学科/年级/标签三级筛选
   - 实现试卷预览、下载、编辑功能
   - 实现个人"已提交/已审核"资源追踪
   - 实现收藏高频使用试卷功能

2. **权限系统集成**
   - 创建校本题库相关权限
   - 配置角色权限关系
   - 实现不同角色的权限控制
   - 集成现有用户权限系统

### 第三阶段（3周）

#### 第9周：高级功能完善

1. **知识点管理集成**
   - 实现学校自定义知识点树管理
   - 实现试卷绑定知识点功能
   - 集成现有知识点管理模块

2. **教材版本管理集成**
   - 实现学校自定义教材版本管理
   - 实现试卷绑定教材版本和章节功能
   - 集成现有教材版本管理模块

3. **高级检索功能**
   - 实现多条件组合检索
   - 实现检索结果排序
   - 优化检索响应时间

#### 第10周：系统测试和优化

1. **功能测试**
   - 编写测试用例
   - 进行单元测试
   - 进行集成测试
   - 进行性能测试

2. **系统优化**
   - 优化数据库查询性能
   - 优化页面加载速度
   - 优化并发处理能力
   - 实现缓存机制

3. **安全性测试**
   - 进行权限控制测试
   - 进行数据传输加密测试
   - 进行操作日志记录测试

#### 第11周：用户培训和上线

1. **文档编写**
   - 编写用户操作手册
   - 编写系统管理员手册
   - 编写API接口文档

2. **用户培训**
   - 培训教师用户
   - 培训备课组长用户
   - 培训学校管理者用户

3. **系统上线**
   - 数据迁移
   - 系统部署
   - 上线监控
   - 问题响应与修复

## 技术实现要点

### 数据库设计要点

1. **校本资源表设计**
   - 试卷数据依然存在原有的 `paper` 表中
   - `school_paper` 表只保存校本库特有的数据，如标签、审核状态等
   - 通过 `paper_id` 关联原有试卷数据

2. **标签系统设计**
   - 支持多级分类
   - 区分系统级标签和自定义标签
   - 实现学科隔离

3. **审核流程设计**
   - 记录完整的审核流水
   - 支持批注功能
   - 支持退回修改和重新提交

### 接口设计要点

1. **RESTful API设计**
   - 遵循RESTful设计规范
   - 与现有试卷管理接口兼容
   - 支持批量操作接口

2. **权限控制**
   - 基于角色的权限控制
   - 学科级别的权限隔离
   - 操作级别的权限控制

### 前端实现要点

1. **界面集成**
   - 保留原有套卷录入界面
   - 新增"校本题库"入口标签页
   - 复用现有试卷预览组件

2. **交互优化**
   - 简洁直观的操作流程
   - 响应式设计，支持移动端
   - 批量操作支持

## 风险管理

1. **技术风险应对**
   - 现有数据库结构改动风险：进行充分的测试和备份
   - 权限系统集成复杂度：采用渐进式开发，先实现基础功能
   - 文件存储容量增长：监控存储使用情况，必要时扩容

2. **业务风险应对**
   - 用户接受度和使用习惯：提供详细的培训和操作指南
   - 数据质量和标签规范性：建立标签规范和审核标准
   - 审核流程效率：优化审核流程，支持批量操作

## 详细技术实现方案

### 数据库表结构设计

#### 1. school_paper 表（校本资源表）
```sql
CREATE TABLE `school_paper` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) NOT NULL COMMENT '试卷ID',
  `school_id` int(11) NOT NULL COMMENT '学校ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-审核退回',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` int(11) DEFAULT NULL COMMENT '审核时间',
  `audit_comment` text COMMENT '审核批注',
  `created_at` int(11) NOT NULL,
  `updated_at` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paper_school` (`paper_id`, `school_id`),
  KEY `idx_school_status` (`school_id`, `status`),
  KEY `idx_audit_user` (`audit_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校本资源表';
```

#### 2. school_paper_tag 表（校本标签表）
```sql
CREATE TABLE `school_paper_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `school_id` int(11) DEFAULT NULL COMMENT '学校ID，NULL表示系统级标签',
  `course_code` varchar(30) NOT NULL COMMENT '课程代码',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '类型：0-系统级，1-自定义',
  `parent_id` int(11) DEFAULT NULL COMMENT '父级标签ID',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` int(11) NOT NULL,
  `updated_at` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_school_course` (`school_id`, `course_code`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校本标签表';
```

#### 3. school_paper_tag_relation 表（校本试卷标签关联表）
```sql
CREATE TABLE `school_paper_tag_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) NOT NULL COMMENT '试卷ID',
  `tag_id` int(11) NOT NULL COMMENT '标签ID',
  `is_main` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否主标签：0-否，1-是',
  `created_at` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paper_tag` (`paper_id`, `tag_id`),
  KEY `idx_tag` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校本试卷标签关联表';
```

#### 4. school_paper_audit_log 表（校本审核流水表）
```sql
CREATE TABLE `school_paper_audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) NOT NULL COMMENT '试卷ID',
  `school_id` int(11) NOT NULL COMMENT '学校ID',
  `status` tinyint(4) NOT NULL COMMENT '审核状态：0-提交审核，1-审核通过，2-审核退回',
  `comment` text COMMENT '审核意见',
  `created_by` int(11) NOT NULL COMMENT '操作人ID',
  `created_at` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_paper` (`paper_id`),
  KEY `idx_school` (`school_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校本审核流水表';
```

### 核心业务逻辑实现

#### SchoolPaper 模型说明

`SchoolPaper` 模型用于管理校本试卷的审核状态：

1. **审核状态**：
   - `STATUS_PENDING`：待审核
   - `STATUS_APPROVED`：已通过
   - `STATUS_REJECTED`：已退回

2. **关联关系**：
   - `school`：所属学校
   - `creator`：创建人
   - `updater`：更新人
   - `auditor`：审核人
   - `tagRelations`：标签关联关系
   - `auditLogs`：审核日志

3. **主要方法**：
   - `submitForAudit()`：提交审核
   - `approve()`：审核通过
   - `reject()`：审核退回
   - `getSchoolStatistics()`：获取学校统计数据

#### SchoolPaperTag 模型说明

`SchoolPaperTag` 模型已更新，主要变化：

1. **移除字段**：
   - `stage`（学段）字段已移除

2. **新增字段**：
   - `created_by`：创建人ID
   - `updated_by`：更新人ID

3. **自动行为**：
   - 使用 `TimestampBehavior` 自动管理 `created_at` 和 `updated_at`
   - 使用 `BlameableBehavior` 自动管理 `created_by` 和 `updated_by`

4. **关联关系**：
   - `creator`：创建人用户信息
   - `updater`：更新人用户信息
   - `school`：所属学校
   - `parent`：父级标签
   - `children`：子级标签
   - `tagRelations`：标签关联关系

5. **主要方法**：
   - `getTagTree()`：获取标签树结构
   - `getFullPath()`：获取标签完整路径
   - `getAllChildrenIds()`：获取所有子标签ID
   - 删除前自动检查关联数据

#### SchoolPaperTagRelation 模型说明

`SchoolPaperTagRelation` 模型用于管理试卷与标签的关联关系：

1. **主要功能**：
   - 管理试卷与标签的多对多关系
   - 支持主标签概念（is_main字段）
   - 提供批量操作方法

2. **关联关系**：
   - `paper`：关联的试卷
   - `tag`：关联的标签

3. **主要方法**：
   - `setTagsForPaper()`：为试卷批量设置标签
   - `getTagsForPaper()`：获取试卷的所有标签
   - `getMainTagForPaper()`：获取试卷的主标签
   - `getPaperIdsByTags()`：根据标签获取试卷ID

#### SchoolPaperAuditLog 模型说明

`SchoolPaperAuditLog` 模型用于记录校本试卷的审核历史：

1. **审核状态**：
   - `STATUS_SUBMITTED`：提交审核
   - `STATUS_APPROVED`：审核通过
   - `STATUS_REJECTED`：审核退回

2. **主要方法**：
   - `log()`：记录审核日志
   - `getAuditHistory()`：获取审核历史
   - `getAuditStatistics()`：获取审核统计数据

#### 1. 试卷提交到校本题库流程
```php
class SchoolPaperService
{
    public function submitToSchoolLibrary($paperId, $schoolId, $userId)
    {
        // 1. 检查试卷是否已存在于校本库
        $existingSchoolPaper = SchoolPaper::find()
            ->where(['paper_id' => $paperId, 'school_id' => $schoolId])
            ->one();

        if ($existingSchoolPaper) {
            throw new Exception('试卷已存在于校本库中');
        }

        // 2. 创建校本资源记录
        $schoolPaper = new SchoolPaper();
        $schoolPaper->paper_id = $paperId;
        $schoolPaper->school_id = $schoolId;
        $schoolPaper->status = SchoolPaperStatus::PENDING;
        $schoolPaper->save();

        // 3. 记录审核日志
        $auditLog = new PaperAuditLog();
        $auditLog->paper_id = $paperId;
        $auditLog->school_id = $schoolId;
        $auditLog->status = SchoolPaperStatus::PENDING;
        $auditLog->comment = '提交审核';
        $auditLog->created_by = $userId;
        $auditLog->save();

        return $schoolPaper;
    }
}
```

#### 2. 审核流程实现
```php
class SchoolPaperAuditService
{
    public function auditPaper($schoolPaperId, $status, $comment, $tagIds, $userId)
    {
        $schoolPaper = SchoolPaper::findOne($schoolPaperId);
        if (!$schoolPaper) {
            throw new Exception('校本资源不存在');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 1. 更新审核状态
            $schoolPaper->status = $status;
            $schoolPaper->audit_user_id = $userId;
            $schoolPaper->audit_time = time();
            $schoolPaper->audit_comment = $comment;
            $schoolPaper->save();

            // 2. 如果审核通过，绑定标签
            if ($status == SchoolPaperStatus::APPROVED && !empty($tagIds)) {
                $this->bindTags($schoolPaper->paper_id, $tagIds);
            }

            // 3. 记录审核日志
            $auditLog = new PaperAuditLog();
            $auditLog->paper_id = $schoolPaper->paper_id;
            $auditLog->school_id = $schoolPaper->school_id;
            $auditLog->status = $status;
            $auditLog->comment = $comment;
            $auditLog->created_by = $userId;
            $auditLog->save();

            $transaction->commit();
            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    private function bindTags($paperId, $tagIds)
    {
        // 删除原有标签关联
        PaperTagRelation::deleteAll(['paper_id' => $paperId]);

        // 绑定新标签
        foreach ($tagIds as $index => $tagId) {
            $relation = new PaperTagRelation();
            $relation->paper_id = $paperId;
            $relation->tag_id = $tagId;
            $relation->is_main = ($index == 0) ? 1 : 0; // 第一个为主标签
            $relation->save();
        }
    }
}
```

### API接口设计

#### 1. 校本标签管理接口
```php
// GET /api/school-paper-tags - 获取校本标签列表
// POST /api/school-paper-tags - 创建校本标签
// PUT /api/school-paper-tags/{id} - 更新校本标签
// DELETE /api/school-paper-tags/{id} - 删除校本标签

class SchoolPaperTagApiController extends ApiController
{
    public function actionIndex()
    {
        $schoolId = $this->getCurrentSchoolId();
        $courseCode = Yii::$app->request->get('course_code');

        $query = PaperTag::find()
            ->where(['or', ['school_id' => $schoolId], ['school_id' => null]])
            ->andWhere(['course_code' => $courseCode])
            ->orderBy(['type' => SORT_ASC, 'sort_order' => SORT_ASC]);

        return $this->success($query->all());
    }
}
```

#### 2. 校本题库检索接口
```php
// GET /api/school-papers - 获取校本题库列表
class SchoolPaperApiController extends ApiController
{
    public function actionIndex()
    {
        $schoolId = $this->getCurrentSchoolId();
        $courseCode = Yii::$app->request->get('course_code');
        $grade = Yii::$app->request->get('grade');
        $tagIds = Yii::$app->request->get('tag_ids', []);

        $query = Paper::find()
            ->joinWith('schoolPaper')
            ->where(['school_paper.school_id' => $schoolId])
            ->andWhere(['school_paper.status' => SchoolPaperStatus::APPROVED])
            ->andWhere(['paper.course_code' => $courseCode]);

        if ($grade) {
            $query->andWhere(['paper.grade' => $grade]);
        }

        if (!empty($tagIds)) {
            $query->joinWith('tagRelations')
                ->andWhere(['paper_tag_relation.tag_id' => $tagIds]);
        }

        return $this->success($query->all());
    }
}
```

### 权限控制实现

#### 1. 权限定义
```php
// 在权限配置中定义
$permissions = [
    'school-paper-manage' => '校本题库管理',  // 学科组长权限，绑定学科和年级
    'school-paper-audit' => '校本题库审核',   // 学科组长权限，绑定学科和年级
    'paper-tag-manage' => '标签管理',        // 学科组长权限，绑定学科和年级
];

$roles = [
    'school-admin' => '学校管理员',
    'course-leader' => '课程组长',  // 现在支持课程和年级绑定
];

// 注意：
// - 不定义教师角色，所有学校用户默认拥有基础权限
// - 校本题库查看和提交权限对所有学校用户开放，无需角色和权限验证
// - 后台管理系统使用独立的权限系统，不与前台权限系统混合
```

#### 1.1 课程组长权限范围配置
课程组长的权限现在与具体的课程和年级绑定，通过 `CourseLeaderScope` 模型管理：

```php
// 为用户分配课程组长权限范围
$scopes = [
    ['course_code' => 'math', 'grade' => 7],      // 数学七年级
    ['course_code' => 'math', 'grade' => 8],      // 数学八年级
    ['course_code' => 'physics', 'grade' => null], // 物理所有年级
];

CourseLeaderScope::setUserPermissionScope($userId, $schoolId, $scopes);
```

#### 2. 权限检查
```php
use common\helpers\PermissionHelper;

class SchoolPaperSearchController extends Controller
{
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        // 校本题库查看权限：学校内所有用户都有此权限
        if (!PermissionHelper::canViewSchoolPaper()) {
            throw new ForbiddenHttpException('您没有访问权限');
        }

        return true;
    }

    // 查看校本题库（所有学校用户都可以访问）
    public function actionIndex()
    {
        // 无需特殊权限验证，学校用户都可以查看
        $query = Paper::find();
        $papers = $query->all();

        return $this->render('index', ['papers' => $papers]);
    }

    // 提交到校本题库（所有学校用户都可以提交）
    public function actionSubmit()
    {
        if (!PermissionHelper::canSubmitSchoolPaper()) {
            throw new ForbiddenHttpException('您没有提交权限');
        }

        // 提交逻辑...
    }

    // 管理校本题库（需要课程组长权限，绑定课程和年级）
    public function actionManage($courseCode = null, $grade = null)
    {
        // 检查是否有管理权限，并验证课程和年级
        if (!PermissionHelper::canManageSchoolPaper($courseCode, $grade)) {
            throw new ForbiddenHttpException('您没有管理该课程年级的权限');
        }

        // 获取用户有权限的数据
        $query = Paper::find();
        PermissionHelper::applyPermissionFilter($query, 'course_code', 'grade');

        // 如果指定了课程和年级，添加额外过滤
        if ($courseCode) {
            $query->andWhere(['course_code' => $courseCode]);
        }
        if ($grade) {
            $query->andWhere(['grade' => $grade]);
        }

        $papers = $query->all();

        return $this->render('manage', ['papers' => $papers]);
    }

    // 审核校本题库（需要课程组长权限，绑定课程和年级）
    public function actionAudit($courseCode, $grade = null)
    {
        // 检查是否有审核权限，必须验证课程和年级
        if (!PermissionHelper::canAuditSchoolPaper($courseCode, $grade)) {
            throw new ForbiddenHttpException('您没有审核该课程年级的权限');
        }

        // 审核逻辑...
    }

    // 标签管理（需要课程组长权限，绑定课程和年级）
    public function actionManageTag($courseCode, $grade = null)
    {
        // 检查是否有标签管理权限
        if (!PermissionHelper::canManagePaperTag($courseCode, $grade)) {
            throw new ForbiddenHttpException('您没有管理该课程年级标签的权限');
        }

        // 标签管理逻辑...
    }
}
```

#### 3. 权限系统初始化和管理

##### 3.1 初始化权限系统
```bash
# 运行数据库迁移
php yii migrate

# 初始化RBAC权限
php yii rbac/init
```

##### 3.2 管理用户权限
```bash
# 为用户分配课程组长权限
php yii rbac/assign-course-leader 123 1 math 7
# 参数：用户ID 学校ID 课程代码 年级（可选）

# 移除课程组长权限
php yii rbac/revoke-course-leader 123 1
# 参数：用户ID 学校ID

# 查看用户权限
php yii rbac/view-user-permissions 123
```

##### 3.3 在代码中使用权限验证
```php
use common\helpers\PermissionHelper;

// 检查基础权限（学校用户都有的权限）
if (PermissionHelper::canViewSchoolPaper()) {
    // 可以查看校本题库
}

if (PermissionHelper::canSubmitSchoolPaper()) {
    // 可以提交到校本题库
}

// 检查特殊权限（需要学科组长权限，绑定学科和年级）
if (PermissionHelper::canManageSchoolPaper('math', 7)) {
    // 可以管理数学七年级的校本题库
}

if (PermissionHelper::canAuditSchoolPaper('physics')) {
    // 可以审核物理科目的校本题库
}

if (PermissionHelper::canManagePaperTag('english', 9)) {
    // 可以管理英语九年级的标签
}

// 获取用户有权限的科目
$courses = PermissionHelper::getUserCourses();

// 获取用户在指定科目下有权限的年级
$grades = PermissionHelper::getUserGrades('math');

// 为查询添加权限过滤（仅对需要特殊权限的操作）
$query = Paper::find();
PermissionHelper::applyPermissionFilter($query, 'course_code', 'grade');
$papers = $query->all(); // 只返回用户有权限管理的试卷
```

#### 4. 权限验证规则说明

权限分为两类：

**基础权限（所有学校用户都有）：**
- 校本题库查看：所有学校用户都可以查看
- 提交到校本题库：所有学校用户都可以提交

**特殊权限（需要课程组长权限，绑定课程和年级）：**
- 校本题库管理：需要课程组长权限，并验证课程和年级
- 校本题库审核：需要课程组长权限，并验证课程和年级
- 标签管理：需要课程组长权限，并验证课程和年级

**角色层级：**
- **课程组长（course-leader）**：特殊角色，需要手动分配，绑定课程和年级
- **学校管理员（school-admin）**：学校最高权限，不受课程年级限制

**权限验证规则：**
1. **默认权限**：所有学校用户都拥有查看和提交权限，无需角色验证
2. **课程组长权限**：验证用户是否具有 `course-leader` 角色，并验证课程和年级范围
3. **学校管理员特权**：学校管理员不受课程年级限制
4. **全年级权限**：如果权限范围中年级为 `null`，表示对该课程的所有年级都有权限
5. **权限隔离**：前台学校业务权限与后台管理权限完全分离

#### 5. 权限管理方式

学科组长权限通过命令行或代码进行管理，不在后台管理界面中提供：

##### 5.1 通过服务类管理权限
```php
use common\services\CourseLeaderPermissionService;

// 分配课程组长权限
$scopes = [
    ['course_code' => 'math', 'grade' => 7],
    ['course_code' => 'math', 'grade' => 8],
    ['course_code' => 'physics', 'grade' => null], // 所有年级
];

$success = CourseLeaderPermissionService::assignCourseLeader($userId, $schoolId, $scopes);

// 移除课程组长权限
CourseLeaderPermissionService::revokeCourseLeader($userId, $schoolId);

// 更新权限范围
CourseLeaderPermissionService::updatePermissionScope($userId, $schoolId, $newScopes);

// 获取用户权限范围
$userScopes = CourseLeaderPermissionService::getUserPermissionScope($userId, $schoolId);

// 获取学校所有课程组长
$leaders = CourseLeaderPermissionService::getSchoolCourseLeaders($schoolId);

// 校本标签管理
use common\models\paper\SchoolPaperTag;
use common\models\paper\SchoolPaperTagRelation;
use common\models\paper\SchoolPaperAuditLog;
```

#### 6. 数据库表结构

#### 7. 校本题库相关表总结

新增的校本题库相关表：

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `school_paper` | 校本试卷表 | paper_id, school_id, status, audit_user_id |
| `course_leader_scope` | 课程组长权限范围表 | user_id, school_id, course_code, grade |
| `school_paper_tag` | 校本标签表 | name, school_id, course_code, type, parent_id |
| `school_paper_tag_relation` | 校本试卷标签关联表 | paper_id, tag_id, is_main |
| `school_paper_audit_log` | 校本审核流水表 | paper_id, school_id, status, comment |

**设计原则：**
- 所有校本题库相关表都加上 `school` 前缀，与公共题库表区分
- 表中不使用外键约束，通过应用层保证数据一致性
- 使用唯一索引防止重复分配权限
- 通过普通索引提高查询性能
- 支持多校SaaS架构，公共题库和校本题库数据隔离

#### 8. 数据库迁移文件清单

以下是校本题库模块需要的所有迁移文件：

| 迁移文件 | 说明 | 状态 |
|----------|------|------|
| `m240722_000001_create_course_leader_scope_table.php` | 创建课程组长权限范围表 | ✅ 已创建 |
| `m240722_000002_modify_paper_tag_table.php` | 修改标签表结构（移除stage，添加created_by/updated_by） | ✅ 已创建 |
| `m240722_000003_create_school_paper_tables.php` | 创建校本题库相关表（标签、关联、审核日志） | ✅ 已创建 |
| `m240722_000004_create_school_paper_table.php` | 创建校本试卷主表 | ✅ 已创建 |

#### 9. 模型文件清单

以下是校本题库模块的所有模型文件：

| 模型文件 | 表名 | 说明 | 状态 |
|----------|------|------|------|
| `CourseLeaderScope.php` | `course_leader_scope` | 课程组长权限范围模型 | ✅ 已创建 |
| `SchoolPaper.php` | `school_paper` | 校本试卷主模型 | ✅ 已创建 |
| `SchoolPaperTag.php` | `school_paper_tag` | 校本标签模型 | ✅ 已创建 |
| `SchoolPaperTagRelation.php` | `school_paper_tag_relation` | 试卷标签关联模型 | ✅ 已创建 |
| `SchoolPaperAuditLog.php` | `school_paper_audit_log` | 审核日志模型 | ✅ 已创建 |

#### 10. 控制器文件清单

以下是校本题库模块需要开发的控制器：

| 控制器文件 | 说明 | 开发状态 |
|------------|------|----------|
| `SchoolPaperTagController.php` | 校本标签管理后台控制器 | 📋 待开发 |
| `SchoolPaperTagApiController.php` | 校本标签API控制器 | 📋 待开发 |
| `SchoolPaperAuditController.php` | 校本审核管理后台控制器 | 📋 待开发 |
| `SchoolPaperAuditApiController.php` | 校本审核API控制器 | 📋 待开发 |
| `SchoolPaperDashboardController.php` | 校本题库看板控制器 | 📋 待开发 |
| `SchoolPaperSearchController.php` | 校本题库检索控制器 | 📋 待开发 |
| `SchoolPaperApiController.php` | 校本题库API控制器 | 📋 待开发 |

#### 11. 服务类文件清单

以下是校本题库模块需要开发的服务类：

| 服务类文件 | 说明 | 开发状态 |
|------------|------|----------|
| `CourseLeaderPermissionService.php` | 课程组长权限管理服务 | ✅ 已创建 |
| `SchoolPaperService.php` | 校本试卷业务服务 | 📋 待开发 |
| `SchoolPaperAuditService.php` | 校本审核业务服务 | 📋 待开发 |
| `SchoolPaperStatisticsService.php` | 校本题库统计服务 | 📋 待开发 |

#### 12. 校本题库模型使用示例

```php
use common\models\paper\SchoolPaper;
use common\models\paper\SchoolPaperTag;
use common\models\paper\SchoolPaperTagRelation;
use common\models\paper\SchoolPaperAuditLog;

// 提交试卷到校本题库
$schoolPaper = SchoolPaper::submitForAudit($paperId, $schoolId, $userId);

// 审核通过
$schoolPaper->approve('审核通过', $auditUserId);

// 审核退回
$schoolPaper->reject('需要修改', $auditUserId);

// 创建校本标签时自动记录创建人
$tag = new SchoolPaperTag();
$tag->name = '期中考试';
$tag->course_code = 'math';
$tag->school_id = 1;
$tag->save(); // created_by 和 updated_by 自动设置

// 获取创建人信息
$creator = $tag->creator;
$updater = $tag->updater;

// 记录审核日志
SchoolPaperAuditLog::log($paperId, $schoolId, SchoolPaperAuditLog::STATUS_APPROVED, '审核通过');

// 为试卷设置标签
SchoolPaperTagRelation::setTagsForPaper($paperId, [$tagId1, $tagId2], $mainTagId);

// 获取试卷的所有标签
$tags = SchoolPaperTagRelation::getTagsForPaper($paperId);

// 获取试卷的主标签
$mainTag = SchoolPaperTagRelation::getMainTagForPaper($paperId);

// 获取标签树结构
$tagTree = SchoolPaperTag::getTagTree($schoolId, 'math');

// 获取标签的完整路径
$fullPath = $tag->getFullPath();

// 获取审核统计
$stats = SchoolPaperAuditLog::getAuditStatistics($schoolId, '2024-01-01', '2024-12-31');

// 获取审核历史
$history = SchoolPaperAuditLog::getAuditHistory($paperId, $schoolId);

// 根据标签查询试卷
$paperIds = SchoolPaperTagRelation::getPaperIdsByTags([$tagId1, $tagId2], true); // 匹配所有标签
```
```

## 开发进度跟踪

### 第一阶段进度（4周）

| 任务 | 预计时间 | 实际状态 | 完成度 |
|------|----------|----------|--------|
| 数据库表设计与创建 | 1天 | ✅ 已完成 | 100% |
| 权限系统设计与实现 | 2天 | ✅ 已完成 | 100% |
| 基础模型类开发 | 2天 | ✅ 已完成 | 100% |
| 标签管理功能 | 1周 | 📋 待开发 | 0% |
| 审核流程功能 | 1周 | 📋 待开发 | 0% |
| 教师端上传功能 | 3天 | 📋 待开发 | 0% |

### 第二阶段进度（4周）

| 任务 | 预计时间 | 实际状态 | 完成度 |
|------|----------|----------|--------|
| 管理者看板开发 | 1周 | 📋 待开发 | 0% |
| 教师端检索功能 | 1周 | 📋 待开发 | 0% |
| 权限系统集成 | 3天 | 📋 待开发 | 0% |
| 系统测试与优化 | 4天 | 📋 待开发 | 0% |

### 当前已完成的核心组件

✅ **数据库层**：
- 所有表结构设计完成
- 迁移文件创建完成
- 索引和约束设计完成

✅ **模型层**：
- 5个核心模型类完成
- 关联关系定义完成
- 业务方法实现完成

✅ **权限系统**：
- 课程组长权限模型完成
- 权限服务类完成
- 权限验证规则完成

📋 **待开发组件**：
- 控制器层（7个控制器）
- 服务层（3个服务类）
- 前端界面
- API接口
- 测试用例

## 测试计划

### 1. 单元测试
- 模型类测试
- 服务类测试
- 工具类测试

### 2. 集成测试
- API接口测试
- 权限控制测试
- 数据库操作测试

### 3. 功能测试
- 用户界面测试
- 业务流程测试
- 异常情况测试

### 4. 性能测试
- 并发访问测试
- 大数据量测试
- 响应时间测试

## 部署方案

### 1. 数据库迁移
- 执行数据库迁移脚本
- 初始化系统级标签数据
- 配置权限数据

### 2. 代码部署
- 部署新增代码文件
- 更新配置文件
- 重启应用服务

### 3. 监控与维护
- 设置系统监控
- 配置日志记录
- 建立问题响应机制

## 开发规范与最佳实践

### 1. 代码规范
- 遵循PSR-4自动加载规范
- 使用Yii2框架的编码规范
- 统一的命名规范：类名使用PascalCase，方法名使用camelCase
- 完善的注释和文档

### 2. 数据库规范
- 表名使用下划线分隔的小写字母
- 字段名使用下划线分隔的小写字母
- 必须有主键和时间戳字段
- 合理使用索引提高查询性能

### 3. API设计规范
- 遵循RESTful设计原则
- 统一的响应格式
- 完善的错误处理机制
- API版本控制

### 4. 安全规范
- 输入验证和过滤
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

## 质量保证措施

### 1. 代码审查
- 每个功能模块完成后进行代码审查
- 检查代码质量和规范性
- 确保安全性和性能

### 2. 自动化测试
- 编写单元测试用例
- 集成测试自动化
- 持续集成和部署

### 3. 性能优化
- 数据库查询优化
- 缓存策略实施
- 前端资源优化

## 项目管理建议

### 1. 团队协作
- 使用Git进行版本控制
- 建立分支管理策略
- 定期代码合并和冲突解决

### 2. 进度管理
- 每周进度汇报
- 里程碑节点检查
- 风险识别和应对

### 3. 沟通协调
- 定期团队会议
- 及时问题反馈
- 跨部门协调

## 上线后运维

### 1. 监控指标
- 系统性能监控
- 用户行为分析
- 错误日志监控

### 2. 数据备份
- 定期数据备份
- 备份恢复测试
- 灾难恢复预案

### 3. 用户支持
- 用户反馈收集
- 问题快速响应
- 功能优化迭代

## 成功验收标准

### 1. 功能验收
- [ ] 管理者看板数据准确展示，包含所有统计维度
- [ ] 审核流程完整可用，支持批量操作和批注功能
- [ ] 标签管理功能正常，支持多级分类和学科隔离
- [ ] 教师端检索和上传功能正常，响应时间<2秒
- [ ] 权限控制有效，不同角色权限隔离

### 2. 性能验收
- [ ] 检索响应时间<2秒
- [ ] 文件上传支持最大50MB，稳定可靠
- [ ] 并发访问500+用户正常
- [ ] 系统可用性≥99.5%

### 3. 集成验收
- [ ] 与现有系统无缝集成，不影响原有功能
- [ ] 数据迁移完整，无数据丢失
- [ ] 用户体验一致，界面风格统一

### 4. 安全验收
- [ ] 角色权限严格控制，无越权访问
- [ ] 数据传输加密，防止信息泄露
- [ ] 操作日志完整记录，可追溯

### 5. 用户验收
- [ ] 教师活跃使用率>80%
- [ ] 试卷复用率>60%
- [ ] 审核通过率>90%
- [ ] 用户满意度>4.0/5.0

## 后续优化方向

### 1. 功能扩展
- AI智能推荐试卷
- 试卷质量评估
- 学习数据分析

### 2. 技术优化
- 微服务架构改造
- 大数据处理能力
- 移动端原生应用

### 3. 生态建设
- 第三方资源接入
- 开放API平台
- 教育资源共享

---

**注意事项：**
1. 本开发步骤基于现有系统架构，需要与现有代码充分兼容
2. 开发过程中要注意数据安全和用户隐私保护
3. 所有功能开发完成后需要进行充分测试
4. 上线前需要进行用户培训和系统演示
5. 建议采用灰度发布策略，逐步推广使用
