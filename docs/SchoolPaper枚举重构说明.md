# SchoolPaper 状态字段枚举重构说明

## 概述

本次重构将 `SchoolPaper` 模型的 `status` 字段从使用常量改为使用 PHP 8.1+ 的枚举类型，提高了代码的类型安全性和可维护性。

## 修改内容

### 1. 新增枚举类

#### SchoolPaperStatus 枚举
- **文件路径**: `common/enums/SchoolPaperStatus.php`
- **用途**: 表示校本试卷的审核状态
- **枚举值**:
  - `Pending = 0` - 待审核
  - `Approved = 1` - 已通过  
  - `Rejected = 2` - 已退回

#### SchoolPaperAuditStatus 枚举
- **文件路径**: `common/enums/SchoolPaperAuditStatus.php`
- **用途**: 表示校本试卷审核日志的操作类型
- **枚举值**:
  - `Submitted = 0` - 提交审核
  - `Approved = 1` - 审核通过
  - `Rejected = 2` - 审核退回

### 2. 修改的模型文件

#### SchoolPaper 模型 (`common/models/paper/SchoolPaper.php`)

**主要修改**:
1. 移除了常量定义：
   ```php
   // 删除了这些常量
   public const STATUS_PENDING = 0;
   public const STATUS_APPROVED = 1; 
   public const STATUS_REJECTED = 2;
   ```

2. 更新了 `rules()` 方法：
   ```php
   // 原来
   [['status'], 'in', 'range' => [self::STATUS_PENDING, self::STATUS_APPROVED, self::STATUS_REJECTED]],
   [['status'], 'default', 'value' => self::STATUS_PENDING],
   
   // 现在
   [['status'], EnumValidator::class, 'enumClass' => SchoolPaperStatus::class],
   [['status'], 'default', 'value' => SchoolPaperStatus::Pending],
   ```

3. 简化了 `getStatusText()` 方法：
   ```php
   // 原来
   public function getStatusText(): string
   {
       $statusMap = [
           self::STATUS_PENDING => '待审核',
           self::STATUS_APPROVED => '已通过',
           self::STATUS_REJECTED => '已退回',
       ];
       return $statusMap[$this->status] ?? '未知状态';
   }
   
   // 现在
   public function getStatusText(): string
   {
       return $this->status instanceof SchoolPaperStatus ? $this->status->label() : '未知状态';
   }
   ```

4. 更新了状态判断方法：
   ```php
   // 原来
   public function isPending(): bool
   {
       return $this->status == self::STATUS_PENDING;
   }
   
   // 现在
   public function isPending(): bool
   {
       return $this->status === SchoolPaperStatus::Pending;
   }
   ```

5. 更新了所有状态赋值操作，使用枚举替代常量

#### SchoolPaperAuditLog 模型 (`common/models/paper/SchoolPaperAuditLog.php`)

**主要修改**:
1. 移除了常量定义
2. 更新了 `rules()` 方法使用 `EnumValidator`
3. 简化了 `getStatusText()` 方法
4. 更新了状态判断方法
5. 修改了 `log()` 方法的参数类型：
   ```php
   // 原来
   public static function log(int $paperId, int $schoolId, int $status, ?string $comment = null, ?int $createdBy = null): bool
   
   // 现在
   public static function log(int $paperId, int $schoolId, SchoolPaperAuditStatus $status, ?string $comment = null, ?int $createdBy = null): bool
   ```

### 3. 枚举特性

两个枚举类都实现了以下特性：

1. **实现了 `EnumLabelInterface` 接口**
2. **使用了 `EnumLabelTrait` 特性**
3. **提供了便捷的判断方法**：
   - `isPending()` / `isSubmitted()`
   - `isApproved()`
   - `isRejected()`
4. **提供了标签方法**：
   - `label()` - 获取单个枚举的标签
   - `labels()` - 获取所有枚举的标签映射

## 优势

1. **类型安全**: 编译时就能发现类型错误
2. **IDE 支持**: 更好的代码补全和重构支持
3. **可维护性**: 枚举值和标签集中管理
4. **一致性**: 与项目中其他枚举保持一致的风格
5. **扩展性**: 容易添加新的状态或方法

## 向后兼容性

- 数据库中的值保持不变（0, 1, 2）
- 现有的业务逻辑无需修改
- API 返回的状态值保持一致

## 使用示例

```php
// 创建校本试卷
$schoolPaper = new SchoolPaper();
$schoolPaper->status = SchoolPaperStatus::Pending;

// 状态判断
if ($schoolPaper->status === SchoolPaperStatus::Pending) {
    // 处理待审核状态
}

// 获取状态标签
echo $schoolPaper->status->label(); // 输出: 待审核

// 记录审核日志
SchoolPaperAuditLog::log($paperId, $schoolId, SchoolPaperAuditStatus::Submitted);
```

## 注意事项

1. 需要 PHP 8.1+ 支持
2. 确保所有使用到这些状态的地方都已更新
3. 在数据库查询中使用 `->value` 获取枚举的实际值
