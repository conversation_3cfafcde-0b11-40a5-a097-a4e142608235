<?php

use yii\db\Migration;

/**
 * 修改 school_paper_tag 表结构
 * 1. 移除 stage 字段
 * 2. 添加 created_by 和 updated_by 字段
 */
class m240722_000002_modify_paper_tag_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // 检查表是否存在
        if ($this->db->getTableSchema('{{%school_paper_tag}}') === null) {
            echo "表 school_paper_tag 不存在，跳过修改\n";
            return true;
        }

        // 移除 stage 字段（如果存在）
        if ($this->db->getTableSchema('{{%school_paper_tag}}')->getColumn('stage') !== null) {
            $this->dropColumn('{{%school_paper_tag}}', 'stage');
            echo "已移除 stage 字段\n";
        }

        // 添加 created_by 字段（如果不存在）
        if ($this->db->getTableSchema('{{%school_paper_tag}}')->getColumn('created_by') === null) {
            $this->addColumn('{{%school_paper_tag}}', 'created_by', $this->integer()->comment('创建人ID'));
            echo "已添加 created_by 字段\n";
        }

        // 添加 updated_by 字段（如果不存在）
        if ($this->db->getTableSchema('{{%school_paper_tag}}')->getColumn('updated_by') === null) {
            $this->addColumn('{{%school_paper_tag}}', 'updated_by', $this->integer()->comment('更新人ID'));
            echo "已添加 updated_by 字段\n";
        }

        // 添加索引
        try {
            $this->createIndex('idx-school_paper_tag-created_by', '{{%school_paper_tag}}', 'created_by');
            echo "已添加 created_by 索引\n";
        } catch (\Exception $e) {
            echo "created_by 索引可能已存在\n";
        }

        try {
            $this->createIndex('idx-school_paper_tag-updated_by', '{{%school_paper_tag}}', 'updated_by');
            echo "已添加 updated_by 索引\n";
        } catch (\Exception $e) {
            echo "updated_by 索引可能已存在\n";
        }

        echo "school_paper_tag 表结构修改完成\n";
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // 检查表是否存在
        if ($this->db->getTableSchema('{{%school_paper_tag}}') === null) {
            echo "表 school_paper_tag 不存在，跳过回滚\n";
            return true;
        }

        // 移除索引
        try {
            $this->dropIndex('idx-school_paper_tag-created_by', '{{%school_paper_tag}}');
        } catch (\Exception $e) {
            // 索引可能不存在
        }

        try {
            $this->dropIndex('idx-school_paper_tag-updated_by', '{{%school_paper_tag}}');
        } catch (\Exception $e) {
            // 索引可能不存在
        }

        // 移除字段
        if ($this->db->getTableSchema('{{%school_paper_tag}}')->getColumn('created_by') !== null) {
            $this->dropColumn('{{%school_paper_tag}}', 'created_by');
        }

        if ($this->db->getTableSchema('{{%school_paper_tag}}')->getColumn('updated_by') !== null) {
            $this->dropColumn('{{%school_paper_tag}}', 'updated_by');
        }

        // 重新添加 stage 字段
        if ($this->db->getTableSchema('{{%school_paper_tag}}')->getColumn('stage') === null) {
            $this->addColumn('{{%school_paper_tag}}', 'stage', $this->tinyInteger()->comment('学段'));
        }

        echo "school_paper_tag 表结构回滚完成\n";
    }
}
