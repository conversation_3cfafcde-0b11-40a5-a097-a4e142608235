<?php

use yii\db\Migration;

/**
 * 创建校本题库相关表
 * 包括：school_paper, school_paper_tag, school_paper_tag_relation, school_paper_audit_log
 */
class m240722_000003_create_school_paper_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // 创建校本试卷表
        $this->createSchoolPaperTable();

        // 创建校本标签表
        $this->createSchoolPaperTagTable();

        // 创建校本试卷标签关联表
        $this->createSchoolPaperTagRelationTable();

        // 创建校本审核流水表
        $this->createSchoolPaperAuditLogTable();

        echo "校本题库相关表创建完成\n";
    }

    /**
     * 创建校本标签表
     */
    private function createSchoolPaperTagTable()
    {
        if ($this->db->getTableSchema('{{%school_paper_tag}}') !== null) {
            echo "表 school_paper_tag 已存在，跳过创建\n";
            return;
        }

        $this->createTable('{{%school_paper_tag}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(50)->notNull()->comment('标签名称'),
            'school_id' => $this->integer()->comment('学校ID，NULL表示系统级标签'),
            'course_code' => $this->string(30)->notNull()->comment('课程代码'),
            'type' => $this->tinyInteger()->notNull()->defaultValue(1)->comment('标签类型：0-系统，1-自定义'),
            'parent_id' => $this->integer()->comment('父级标签ID'),
            'sort_order' => $this->integer()->notNull()->defaultValue(0)->comment('排序'),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
            'created_by' => $this->integer()->comment('创建人ID'),
            'updated_by' => $this->integer()->comment('更新人ID'),
        ]);

        // 添加索引
        $this->createIndex('idx-school_paper_tag-school_course', '{{%school_paper_tag}}', ['school_id', 'course_code']);
        $this->createIndex('idx-school_paper_tag-parent', '{{%school_paper_tag}}', 'parent_id');
        $this->createIndex('idx-school_paper_tag-created_by', '{{%school_paper_tag}}', 'created_by');
        $this->createIndex('idx-school_paper_tag-updated_by', '{{%school_paper_tag}}', 'updated_by');
        
        // 添加唯一约束
        $this->createIndex('idx-school_paper_tag-unique', '{{%school_paper_tag}}', 
            ['name', 'school_id', 'course_code', 'parent_id'], true);

        // 添加表注释
        $this->addCommentOnTable('{{%school_paper_tag}}', '校本标签表');
        
        echo "已创建 school_paper_tag 表\n";
    }

    /**
     * 创建校本试卷标签关联表
     */
    private function createSchoolPaperTagRelationTable()
    {
        if ($this->db->getTableSchema('{{%school_paper_tag_relation}}') !== null) {
            echo "表 school_paper_tag_relation 已存在，跳过创建\n";
            return;
        }

        $this->createTable('{{%school_paper_tag_relation}}', [
            'id' => $this->primaryKey(),
            'paper_id' => $this->integer()->notNull()->comment('试卷ID'),
            'tag_id' => $this->integer()->notNull()->comment('标签ID'),
            'is_main' => $this->tinyInteger()->notNull()->defaultValue(0)->comment('是否主标签：0-否，1-是'),
            'created_at' => $this->integer()->notNull(),
        ]);

        // 添加索引
        $this->createIndex('idx-school_paper_tag_relation-paper', '{{%school_paper_tag_relation}}', 'paper_id');
        $this->createIndex('idx-school_paper_tag_relation-tag', '{{%school_paper_tag_relation}}', 'tag_id');
        
        // 添加唯一约束
        $this->createIndex('uk-school_paper_tag_relation-paper_tag', '{{%school_paper_tag_relation}}', 
            ['paper_id', 'tag_id'], true);

        // 添加表注释
        $this->addCommentOnTable('{{%school_paper_tag_relation}}', '校本试卷标签关联表');
        
        echo "已创建 school_paper_tag_relation 表\n";
    }

    /**
     * 创建校本审核流水表
     */
    private function createSchoolPaperAuditLogTable()
    {
        if ($this->db->getTableSchema('{{%school_paper_audit_log}}') !== null) {
            echo "表 school_paper_audit_log 已存在，跳过创建\n";
            return;
        }

        $this->createTable('{{%school_paper_audit_log}}', [
            'id' => $this->primaryKey(),
            'paper_id' => $this->integer()->notNull()->comment('试卷ID'),
            'school_id' => $this->integer()->notNull()->comment('学校ID'),
            'status' => $this->tinyInteger()->notNull()->comment('审核状态：0-提交审核，1-审核通过，2-审核退回'),
            'comment' => $this->text()->comment('审核意见'),
            'created_by' => $this->integer()->notNull()->comment('操作人ID'),
            'created_at' => $this->integer()->notNull(),
        ]);

        // 添加索引
        $this->createIndex('idx-school_paper_audit_log-paper', '{{%school_paper_audit_log}}', 'paper_id');
        $this->createIndex('idx-school_paper_audit_log-school', '{{%school_paper_audit_log}}', 'school_id');
        $this->createIndex('idx-school_paper_audit_log-status', '{{%school_paper_audit_log}}', 'status');
        $this->createIndex('idx-school_paper_audit_log-created_by', '{{%school_paper_audit_log}}', 'created_by');
        $this->createIndex('idx-school_paper_audit_log-created_at', '{{%school_paper_audit_log}}', 'created_at');

        // 添加表注释
        $this->addCommentOnTable('{{%school_paper_audit_log}}', '校本审核流水表');
        
        echo "已创建 school_paper_audit_log 表\n";
    }

    /**
     * 创建校本试卷表
     */
    private function createSchoolPaperTable()
    {
        if ($this->db->getTableSchema('{{%school_paper}}') !== null) {
            echo "表 school_paper 已存在，跳过创建\n";
            return;
        }

        $this->createTable('{{%school_paper}}', [
            'id' => $this->primaryKey(),
            'paper_id' => $this->integer()->notNull()->comment('试卷ID'),
            'school_id' => $this->integer()->notNull()->comment('学校ID'),
            'status' => $this->tinyInteger()->notNull()->defaultValue(0)->comment('状态：0-待审核，1-已通过，2-已退回'),
            'audit_user_id' => $this->integer()->comment('审核人ID'),
            'audit_time' => $this->integer()->comment('审核时间'),
            'audit_comment' => $this->text()->comment('审核意见'),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
            'created_by' => $this->integer()->comment('创建人ID'),
            'updated_by' => $this->integer()->comment('更新人ID'),
        ]);

        // 添加索引
        $this->createIndex('idx-school_paper-paper_id', '{{%school_paper}}', 'paper_id');
        $this->createIndex('idx-school_paper-school_id', '{{%school_paper}}', 'school_id');
        $this->createIndex('idx-school_paper-status', '{{%school_paper}}', 'status');
        $this->createIndex('idx-school_paper-audit_user_id', '{{%school_paper}}', 'audit_user_id');
        $this->createIndex('idx-school_paper-created_by', '{{%school_paper}}', 'created_by');

        // 添加唯一约束
        $this->createIndex('uk-school_paper-paper_school', '{{%school_paper}}', ['paper_id', 'school_id'], true);

        // 添加表注释
        $this->addCommentOnTable('{{%school_paper}}', '校本试卷表');

        echo "已创建 school_paper 表\n";
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%school_paper_audit_log}}');
        $this->dropTable('{{%school_paper_tag_relation}}');
        $this->dropTable('{{%school_paper_tag}}');
        $this->dropTable('{{%school_paper}}');

        echo "校本题库相关表删除完成\n";
    }
}
