<?php

use yii\db\Migration;

/**
 * 创建校本试卷表
 */
class m240722_000004_create_school_paper_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // 检查表是否已存在
        if ($this->db->getTableSchema('{{%school_paper}}') !== null) {
            echo "表 school_paper 已存在，跳过创建\n";
            return true;
        }

        $this->createTable('{{%school_paper}}', [
            'id' => $this->primaryKey(),
            'paper_id' => $this->integer()->notNull()->comment('试卷ID'),
            'school_id' => $this->integer()->notNull()->comment('学校ID'),
            'status' => $this->tinyInteger()->notNull()->defaultValue(0)->comment('状态：0-待审核，1-已通过，2-已退回'),
            'audit_user_id' => $this->integer()->comment('审核人ID'),
            'audit_time' => $this->integer()->comment('审核时间'),
            'audit_comment' => $this->text()->comment('审核意见'),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
            'created_by' => $this->integer()->comment('创建人ID'),
            'updated_by' => $this->integer()->comment('更新人ID'),
        ]);

        // 添加索引
        $this->createIndex('idx-school_paper-paper_id', '{{%school_paper}}', 'paper_id');
        $this->createIndex('idx-school_paper-school_id', '{{%school_paper}}', 'school_id');
        $this->createIndex('idx-school_paper-status', '{{%school_paper}}', 'status');
        $this->createIndex('idx-school_paper-audit_user_id', '{{%school_paper}}', 'audit_user_id');
        $this->createIndex('idx-school_paper-created_by', '{{%school_paper}}', 'created_by');
        $this->createIndex('idx-school_paper-updated_by', '{{%school_paper}}', 'updated_by');
        $this->createIndex('idx-school_paper-created_at', '{{%school_paper}}', 'created_at');
        
        // 添加唯一约束
        $this->createIndex('uk-school_paper-paper_school', '{{%school_paper}}', ['paper_id', 'school_id'], true);

        // 添加表注释
        $this->addCommentOnTable('{{%school_paper}}', '校本试卷表');
        
        echo "已创建 school_paper 表\n";
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%school_paper}}');
        echo "已删除 school_paper 表\n";
    }
}
