<?php

use yii\db\Migration;

/**
 * 创建课程组长权限范围表
 * 用于存储课程组长负责的课程和年级范围
 */
class m240722_000001_create_course_leader_scope_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%course_leader_scope}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull()->comment('用户ID'),
            'school_id' => $this->integer()->notNull()->comment('学校ID'),
            'course_code' => $this->string(30)->notNull()->comment('课程代码'),
            'grade' => $this->tinyInteger()->comment('年级，null表示所有年级'),
            'created_at' => $this->integer(),
            'updated_at' => $this->integer(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        // 添加索引
        $this->createIndex('idx-course_leader_scope-user_id', '{{%course_leader_scope}}', 'user_id');
        $this->createIndex('idx-course_leader_scope-school_id', '{{%course_leader_scope}}', 'school_id');
        $this->createIndex('idx-course_leader_scope-course_code', '{{%course_leader_scope}}', 'course_code');
        $this->createIndex('idx-course_leader_scope-grade', '{{%course_leader_scope}}', 'grade');

        // 添加唯一约束，防止重复分配
        $this->createIndex('idx-course_leader_scope-unique', '{{%course_leader_scope}}',
            ['user_id', 'school_id', 'course_code', 'grade'], true);

        // 添加表注释
        $this->addCommentOnTable('{{%course_leader_scope}}', '课程组长权限范围表');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%course_leader_scope}}');
    }
}
